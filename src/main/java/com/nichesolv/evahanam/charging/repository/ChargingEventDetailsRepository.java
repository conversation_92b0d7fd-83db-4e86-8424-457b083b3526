package com.nichesolv.evahanam.charging.repository;

import com.nichesolv.evahanam.charging.jpa.ChargingEventDetails;
import com.nichesolv.evahanam.charging.jpa.EventIdx;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.Instant;
import java.util.List;

// ChargingEventDetailsRepository.java
public interface ChargingEventDetailsRepository extends JpaRepository<ChargingEventDetails, EventIdx> {

    //method to find avg charging time ie diff(endtime-starttime) if end soc=100,start soc <20

    @Query(value = "with start_values AS (\n" +
            "    SELECT\n" +
            "        charging_event_id,\n" +
            "        vehicle_id,\n" +
            "        field_value\\:\\:numeric AS start_soc\n" +
            "    FROM evdata.charging_event_details\n" +
            "    WHERE field_name = 'start_soc' and vehicle_id=?1 and field_value\\:\\:numeric <?2\n" +
            "),\n" +
            "end_values AS (\n" +
            "    SELECT\n" +
            "        charging_event_id,\n" +
            "        vehicle_id,\n" +
            "        field_value\\:\\:numeric AS end_soc\n" +
            "    FROM evdata.charging_event_details\n" +
            "    WHERE field_name = 'end_soc' and vehicle_id=?1 and field_value\\:\\:numeric>=?3\n" +
            ")\n" +
            "SELECT\n" +
            "    AVG(c.end_time - c.start_time) AS avg_charging_duration\n" +
            "FROM evdata.charging_event c\n" +
            "JOIN start_values s ON c.id = s.charging_event_id AND c.vehicle_id = s.vehicle_id\n" +
            "JOIN end_values e ON c.id = e.charging_event_id AND c.vehicle_id = e.vehicle_id\n" +
            "WHERE c.vehicle_id = ?1 ", nativeQuery = true)
    String findAvgChargingTimeForVehicleWithinStartSocLessThanAndEndSocGreaterThan(Long vehicleId, Integer startSoc, Integer endSoc);

    // Average across a set of vehicles within a time window and SOC constraints
    @Query(value = "with start_values AS (\n" +
            "    SELECT charging_event_id, vehicle_id, CAST(field_value AS numeric) AS start_soc\n" +
            "    FROM evdata.charging_event_details\n" +
            "    WHERE field_name = 'start_soc' and vehicle_id IN (?1) and CAST(field_value AS numeric) <?4\n" +
            "),\n" +
            "end_values AS (\n" +
            "    SELECT charging_event_id, vehicle_id, CAST(field_value AS numeric) AS end_soc\n" +
            "    FROM evdata.charging_event_details\n" +
            "    WHERE field_name = 'end_soc' and vehicle_id IN (?1) and CAST(field_value AS numeric)>=?5\n" +
            ")\n" +
            "SELECT AVG(c.end_time - c.start_time) AS avg_charging_duration\n" +
            "FROM evdata.charging_event c\n" +
            "JOIN start_values s ON c.id = s.charging_event_id AND c.vehicle_id = s.vehicle_id\n" +
            "JOIN end_values e ON c.id = e.charging_event_id AND c.vehicle_id = e.vehicle_id\n" +
            "WHERE c.vehicle_id IN (?1) ", nativeQuery = true)
    String findAvgChargingTimeForVehiclesWithinWindowAndSoc(List<Long> vehicleIds, Integer startSoc, Integer endSoc);
}

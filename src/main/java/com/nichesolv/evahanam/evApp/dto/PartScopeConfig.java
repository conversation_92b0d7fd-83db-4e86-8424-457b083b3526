package com.nichesolv.evahanam.evApp.dto;

import com.nichesolv.evahanam.vehicleModel.enums.PartType;

import java.util.Collections;
import java.util.List;

public enum PartScopeConfig {
    MOTOR(
            List.of(PartType.MOTOR.name(), PartType.MCU.name()),
            List.of(PartType.MCU.name())
    ),
    BATTERY(
            List.of(PartType.BATTERY.name()),
            List.of(PartType.BMS.name())
    ),
    RANGE(
            Collections.emptyList(),
            Collections.emptyList()
    );

    private final List<String> alertParts;
    private final List<String> alarmParts;

    PartScopeConfig(List<String> alertParts, List<String> alarmParts) {
        this.alertParts = alertParts;
        this.alarmParts = alarmParts;
    }

    public List<String> getAlertParts() {
        return alertParts;
    }

    public List<String> getAlarmParts() {
        return alarmParts;
    }
}


package com.nichesolv.evahanam.evApp.dto;

import lombok.Data;
import java.util.Map;

@Data
public class MotorResponseDto {
    // Health distribution across categories (Excellent/Good/Average/Poor) with counts, percentages and IMEIs
    private Map<String, HealthDetailDto> health;
    // Alert breakdown per alert type (e.g., Motor Temperature) with total count and percentages
    private Map<String, AlertBreakdownDto> alerts;
    // Alarm breakdown per alarm name: total count and percentage of total alarms in window
    private Map<String, CountPercentageDto> alarms;
}

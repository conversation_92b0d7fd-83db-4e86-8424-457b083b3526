package com.nichesolv.evahanam.evApp.dto;


import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class RangeResponseDto {
    // For single-vehicle scope
    private Long vehicleId;
    private String imei;
    private String modelName; // model no (name)

    // Aggregates
    private double avgRange;     // within time filter
    private double overallRange; // lifetime

    // Graphs (keys: "day", "week", "month")
    private Map<String, List<GraphPoint>> graph;

    // Present for fleet/model; null for single vehicle
    private List<TopVehicleRow> topVehicles;

    @Data
    public static class GraphPoint {
        private int x;      // integer value on X-axis (trip index/day number)
        private double y;   // avg range value
    }

    @Data
    public static class TopVehicleRow {
        private Long vehicleId;
        private String imei;
        private String modelName; // model no (name)
        private double avgRange;
    }
}